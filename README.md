# Best Wishes Maker - 祝福生成器

一个基于 FastAPI 的现代化 Web 应用，提供用户认证、问卦服务等功能。

## 📋 目录

- [项目介绍](#项目介绍)
- [主要功能](#主要功能)
- [项目结构](#项目结构)
- [技术栈](#技术栈)
- [开发指南](#开发指南)
- [部署指南](#部署指南)
- [API 文档](#api-文档)
- [环境配置](#环境配置)

## 🚀 项目介绍

Best Wishes Maker 是一个集成了用户认证、问卦服务的现代化 Web 应用。支持微信登录、手机号注册、JWT 认证等功能，并提供了完整的用户管理和问卦服务。

## ✨ 主要功能

### 🔐 认证系统
- **多种登录方式**：支持微信登录、手机号登录、邮箱登录
- **JWT 认证**：基于 JWT 的无状态认证机制
- **令牌刷新**：支持访问令牌和刷新令牌机制
- **会话管理**：完整的用户会话记录和管理

### 👥 用户管理
- **用户注册**：支持手机号注册
- **用户信息**：完整的用户资料管理
- **多身份认证**：支持一个用户绑定多种认证方式

### 🔮 问卦服务
- **随机卦象**：基于传统易经64卦的问卦服务
- **AI 解释**：集成 AI 服务生成卦象解释
- **频率限制**：防止滥用的访问频率控制

### 🛡️ 安全特性
- **访问限制**：基于 Redis 的访问频率限制
- **密码加密**：使用 bcrypt 进行密码哈希
- **请求日志**：完整的认证和访问日志记录