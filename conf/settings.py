# -*-encoding: utf-8 -*-
import secrets
from typing import Any, Dict, List, Optional, Union
from urllib.parse import quote

from pydantic import AnyHttpUrl, EmailStr, HttpUrl, AnyUrl, field_validator
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    API_V1_STR: str = "/v1"
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 480
    WX_APPID: str
    WX_SECRET: str
    JSCODE2SESSION_URL: str
    SERVER_NAME: str
    SERVER_HOST: str
    SERVER_PORT: int
    PRODUCT_MODE: bool
    ALGORITHM : str
    AI_API_KEY: str
    AI_BASE_URL: str


    # BACKEND_CORS_ORIGINS is a JSON-formatted list of origins
    # e.g: '["http://localhost", "http://localhost:4200", "http://localhost:3000", \
    # "http://localhost:8080", "http://local.dockertoolbox.tiangolo.com"]'
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(
            cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    PROJECT_NAME: str
    SENTRY_DSN: Optional[HttpUrl] = None

    IT_DSN: Optional[HttpUrl] = None

    @field_validator("SENTRY_DSN", mode="before")
    def sentry_dsn_can_be_blank(cls, v: Optional[str]) -> Optional[str]:
        if v is None or len(v) == 0:
            return None
        return v

    MYSQL_HOST: str
    MYSQL_PORT: str
    MYSQL_USER: str
    MYSQL_PASSWORD: str
    MYSQL_DB: str
    ASYNC_SQLALCHEMY_DATABASE_URI: Optional[str] = None
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @field_validator("ASYNC_SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_async_db_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str):
            return v
        return f"mysql+aiomysql://{info.data.get('MYSQL_USER')}:{info.data.get('MYSQL_PASSWORD')}@{info.data.get('MYSQL_HOST')}:{info.data.get('MYSQL_PORT')}/{info.data.get('MYSQL_DB')}?charset=utf8"
    
    
    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], info) -> str:
        if isinstance(v, str):
            return v
        return f"mysql+pymysql://{info.data.get('MYSQL_USER')}:{info.data.get('MYSQL_PASSWORD')}@{info.data.get('MYSQL_HOST')}:{info.data.get('MYSQL_PORT')}/{info.data.get('MYSQL_DB')}?charset=utf8"

    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48
    EMAIL_TEMPLATES_DIR: str = "/app/app/email-templates/build"
    EMAILS_ENABLED: bool = False

    @field_validator("EMAILS_ENABLED", mode="before")
    def get_emails_enabled(cls, v: bool, info) -> bool:
        return bool(
            info.data.get("SMTP_HOST") and info.data.get("SMTP_PORT")
            and info.data.get("EMAILS_FROM_EMAIL"))

    FIRST_SUPERUSER: EmailStr
    FIRST_SUPERUSER_PASSWORD: str
    USERS_OPEN_REGISTRATION: bool = False
    DEFAULT_PASSWORD: str = "123456"

    REPORT_FROM: Optional[EmailStr] = None

    LOG_LEVEL: str
    SEND_EMAIL_API: Optional[str] = None

    @field_validator("LOG_LEVEL", mode="before")
    def parse_log_level_value(cls, v: Optional[str], info) -> str:
        import logging
        if not v:
            v = "DEBUG"
        level = logging.getLevelName(v)
        return str(level)

    # redis config
    REDIS_HOST: str = "127.0.0.1"
    REDIS_PORT: int = 6379
    REDIS_PSWD: str = ""  # Set password if needed

    class Config:
        case_sensitive = True
        env_file = "conf/.env"


settings = Settings()
