"""init tables

Revision ID: fef36351050d
Revises: f4db280c8d56
Create Date: 2025-05-12 16:09:38.473169

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'fef36351050d'
down_revision: Union[str, None] = 'f4db280c8d56'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('auth_identities', 'credential',
               existing_type=mysql.VARCHAR(length=255),
               comment='加密凭证(密码等)',
               existing_comment='凭证(密码/token等)',
               existing_nullable=True)
    op.drop_index('idx_auth_identity', table_name='auth_identities')
    op.drop_index('ix_auth_identities_identifier', table_name='auth_identities')
    op.create_index('idx_mobile', 'auth_identities', ['mobile'], unique=True, postgresql_where=sa.text("identity_type = 'mobile'"))
    op.create_index('idx_wechat_openid', 'auth_identities', ['wechat_openid'], unique=True, postgresql_where=sa.text("identity_type = 'wechat'"))
    op.drop_column('auth_identities', 'last_login_ip')
    op.drop_column('auth_identities', 'identifier')
    op.drop_column('auth_identities', 'last_login_at')
    op.alter_column('auth_logs', 'user_agent',
               existing_type=mysql.TEXT(),
               type_=sa.String(length=255),
               existing_comment='用户代理',
               existing_nullable=True)
    op.drop_column('auth_logs', 'error_msg')
    op.add_column('user_profiles', sa.Column('province', sa.String(length=50), nullable=True, comment='省份'))
    op.add_column('user_profiles', sa.Column('city', sa.String(length=50), nullable=True, comment='城市'))
    op.drop_column('user_profiles', 'occupation')
    op.drop_column('user_profiles', 'address')
    op.add_column('user_sessions', sa.Column('ip_address', sa.String(length=45), nullable=True, comment='登录IP'))
    op.alter_column('user_sessions', 'auth_identity_id',
               existing_type=mysql.INTEGER(),
               comment='认证方式ID',
               existing_comment='使用的认证方式',
               existing_nullable=True)
    op.alter_column('user_sessions', 'access_token',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=512),
               existing_comment='访问令牌',
               existing_nullable=False)
    op.alter_column('user_sessions', 'refresh_token',
               existing_type=mysql.VARCHAR(length=255),
               type_=sa.String(length=512),
               nullable=False,
               existing_comment='刷新令牌')
    op.drop_column('user_sessions', 'os_version')
    op.drop_column('user_sessions', 'device_name')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_sessions', sa.Column('device_name', mysql.VARCHAR(length=100), nullable=True, comment='设备名称'))
    op.add_column('user_sessions', sa.Column('os_version', mysql.VARCHAR(length=20), nullable=True, comment='系统版本'))
    op.alter_column('user_sessions', 'refresh_token',
               existing_type=sa.String(length=512),
               type_=mysql.VARCHAR(length=255),
               nullable=True,
               existing_comment='刷新令牌')
    op.alter_column('user_sessions', 'access_token',
               existing_type=sa.String(length=512),
               type_=mysql.VARCHAR(length=255),
               existing_comment='访问令牌',
               existing_nullable=False)
    op.alter_column('user_sessions', 'auth_identity_id',
               existing_type=mysql.INTEGER(),
               comment='使用的认证方式',
               existing_comment='认证方式ID',
               existing_nullable=True)
    op.drop_column('user_sessions', 'ip_address')
    op.add_column('user_profiles', sa.Column('address', mysql.VARCHAR(length=255), nullable=True, comment='详细地址'))
    op.add_column('user_profiles', sa.Column('occupation', mysql.VARCHAR(length=100), nullable=True, comment='职业'))
    op.drop_column('user_profiles', 'city')
    op.drop_column('user_profiles', 'province')
    op.add_column('auth_logs', sa.Column('error_msg', mysql.TEXT(), nullable=True, comment='错误详情'))
    op.alter_column('auth_logs', 'user_agent',
               existing_type=sa.String(length=255),
               type_=mysql.TEXT(),
               existing_comment='用户代理',
               existing_nullable=True)
    op.add_column('auth_identities', sa.Column('last_login_at', mysql.DATETIME(), nullable=True, comment='最后登录时间'))
    op.add_column('auth_identities', sa.Column('identifier', mysql.VARCHAR(length=100), nullable=True, comment='唯一标识(邮箱/用户名等)'))
    op.add_column('auth_identities', sa.Column('last_login_ip', mysql.VARCHAR(length=45), nullable=True, comment='最后登录IP'))
    op.drop_index('idx_wechat_openid', table_name='auth_identities', postgresql_where=sa.text("identity_type = 'wechat'"))
    op.drop_index('idx_mobile', table_name='auth_identities', postgresql_where=sa.text("identity_type = 'mobile'"))
    op.create_index('ix_auth_identities_identifier', 'auth_identities', ['identifier'], unique=False)
    op.create_index('idx_auth_identity', 'auth_identities', ['identity_type', 'identifier'], unique=True)
    op.alter_column('auth_identities', 'credential',
               existing_type=mysql.VARCHAR(length=255),
               comment='凭证(密码/token等)',
               existing_comment='加密凭证(密码等)',
               existing_nullable=True)
    # ### end Alembic commands ###
