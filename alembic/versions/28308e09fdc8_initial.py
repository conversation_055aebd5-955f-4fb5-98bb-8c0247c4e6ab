"""initial

Revision ID: 28308e09fdc8
Revises: 
Create Date: 2025-05-11 16:51:17.946037

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '28308e09fdc8'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('status', sa.Integer(), nullable=True, comment='状态: 0=禁用, 1=正常'),
    sa.Column('nickname', sa.String(length=64), nullable=True, comment='昵称'),
    sa.Column('avatar_url', sa.String(length=255), nullable=True, comment='头像URL'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=100), nullable=True, comment='更新人'),
    sa.Column('deleted', sa.SmallInteger(), nullable=True, comment='逻辑删除: 0=正常, 1=删除'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_table('auth_identities',
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('identity_type', sa.Enum('wechat', 'mobile', 'email', 'username'), nullable=False, comment='认证类型'),
    sa.Column('wechat_openid', sa.String(length=64), nullable=True, comment='微信openid'),
    sa.Column('wechat_unionid', sa.String(length=64), nullable=True, comment='微信unionid'),
    sa.Column('session_key', sa.String(length=64), nullable=True, comment='微信会话密钥'),
    sa.Column('mobile', sa.String(length=20), nullable=True, comment='手机号'),
    sa.Column('mobile_verified', sa.Integer(), nullable=True, comment='手机验证: 0=未验证, 1=已验证'),
    sa.Column('identifier', sa.String(length=100), nullable=True, comment='唯一标识(邮箱/用户名等)'),
    sa.Column('credential', sa.String(length=255), nullable=True, comment='凭证(密码/token等)'),
    sa.Column('last_login_at', sa.DateTime(), nullable=True, comment='最后登录时间'),
    sa.Column('last_login_ip', sa.String(length=45), nullable=True, comment='最后登录IP'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=100), nullable=True, comment='更新人'),
    sa.Column('deleted', sa.SmallInteger(), nullable=True, comment='逻辑删除: 0=正常, 1=删除'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_auth_identity', 'auth_identities', ['identity_type', 'identifier'], unique=True)
    op.create_index(op.f('ix_auth_identities_id'), 'auth_identities', ['id'], unique=False)
    op.create_index(op.f('ix_auth_identities_identifier'), 'auth_identities', ['identifier'], unique=False)
    op.create_index(op.f('ix_auth_identities_mobile'), 'auth_identities', ['mobile'], unique=False)
    op.create_index(op.f('ix_auth_identities_user_id'), 'auth_identities', ['user_id'], unique=False)
    op.create_index(op.f('ix_auth_identities_wechat_openid'), 'auth_identities', ['wechat_openid'], unique=False)
    op.create_index(op.f('ix_auth_identities_wechat_unionid'), 'auth_identities', ['wechat_unionid'], unique=False)
    op.create_table('user_profiles',
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('real_name', sa.String(length=50), nullable=True, comment='真实姓名'),
    sa.Column('gender', sa.Enum('male', 'female', 'unknown'), nullable=True, comment='性别'),
    sa.Column('birthday', sa.Date(), nullable=True, comment='生日'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='电子邮箱'),
    sa.Column('address', sa.String(length=255), nullable=True, comment='详细地址'),
    sa.Column('id_card', sa.String(length=18), nullable=True, comment='身份证号'),
    sa.Column('occupation', sa.String(length=100), nullable=True, comment='职业'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=100), nullable=True, comment='更新人'),
    sa.Column('deleted', sa.SmallInteger(), nullable=True, comment='逻辑删除: 0=正常, 1=删除'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'id')
    )
    op.create_index(op.f('ix_user_profiles_id'), 'user_profiles', ['id'], unique=False)
    op.create_table('auth_logs',
    sa.Column('user_id', sa.Integer(), nullable=True, comment='用户ID'),
    sa.Column('auth_identity_id', sa.Integer(), nullable=True, comment='认证方式ID'),
    sa.Column('auth_type', sa.Enum('login', 'register', 'bind', 'unbind', 'change_credential'), nullable=False, comment='操作类型'),
    sa.Column('result', sa.Integer(), nullable=False, comment='结果: 0=失败, 1=成功'),
    sa.Column('ip_address', sa.String(length=45), nullable=True, comment='IP地址'),
    sa.Column('user_agent', sa.Text(), nullable=True, comment='用户代理'),
    sa.Column('device_info', sa.Text(), nullable=True, comment='设备信息'),
    sa.Column('error_code', sa.String(length=50), nullable=True, comment='错误代码'),
    sa.Column('error_msg', sa.Text(), nullable=True, comment='错误详情'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=100), nullable=True, comment='更新人'),
    sa.Column('deleted', sa.SmallInteger(), nullable=True, comment='逻辑删除: 0=正常, 1=删除'),
    sa.ForeignKeyConstraint(['auth_identity_id'], ['auth_identities.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_auth_logs_id'), 'auth_logs', ['id'], unique=False)
    op.create_index(op.f('ix_auth_logs_user_id'), 'auth_logs', ['user_id'], unique=False)
    op.create_table('user_sessions',
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('auth_identity_id', sa.Integer(), nullable=True, comment='使用的认证方式'),
    sa.Column('access_token', sa.String(length=255), nullable=False, comment='访问令牌'),
    sa.Column('refresh_token', sa.String(length=255), nullable=True, comment='刷新令牌'),
    sa.Column('expires_at', sa.DateTime(), nullable=False, comment='过期时间'),
    sa.Column('device_id', sa.String(length=100), nullable=True, comment='设备ID'),
    sa.Column('device_name', sa.String(length=100), nullable=True, comment='设备名称'),
    sa.Column('platform', sa.String(length=20), nullable=True, comment='平台: ios/android/web'),
    sa.Column('os_version', sa.String(length=20), nullable=True, comment='系统版本'),
    sa.Column('id', sa.Integer(), nullable=False, comment='主键ID'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('created_by', sa.String(length=100), nullable=True, comment='创建人'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.Column('updated_by', sa.String(length=100), nullable=True, comment='更新人'),
    sa.Column('deleted', sa.SmallInteger(), nullable=True, comment='逻辑删除: 0=正常, 1=删除'),
    sa.ForeignKeyConstraint(['auth_identity_id'], ['auth_identities.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_sessions_access_token'), 'user_sessions', ['access_token'], unique=True)
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_user_sessions_refresh_token'), 'user_sessions', ['refresh_token'], unique=True)
    op.create_index(op.f('ix_user_sessions_user_id'), 'user_sessions', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_sessions_user_id'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_refresh_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_access_token'), table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index(op.f('ix_auth_logs_user_id'), table_name='auth_logs')
    op.drop_index(op.f('ix_auth_logs_id'), table_name='auth_logs')
    op.drop_table('auth_logs')
    op.drop_index(op.f('ix_user_profiles_id'), table_name='user_profiles')
    op.drop_table('user_profiles')
    op.drop_index(op.f('ix_auth_identities_wechat_unionid'), table_name='auth_identities')
    op.drop_index(op.f('ix_auth_identities_wechat_openid'), table_name='auth_identities')
    op.drop_index(op.f('ix_auth_identities_user_id'), table_name='auth_identities')
    op.drop_index(op.f('ix_auth_identities_mobile'), table_name='auth_identities')
    op.drop_index(op.f('ix_auth_identities_identifier'), table_name='auth_identities')
    op.drop_index(op.f('ix_auth_identities_id'), table_name='auth_identities')
    op.drop_index('idx_auth_identity', table_name='auth_identities')
    op.drop_table('auth_identities')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
