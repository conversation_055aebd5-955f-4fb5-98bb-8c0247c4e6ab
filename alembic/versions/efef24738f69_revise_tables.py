"""revise tables

Revision ID: efef24738f69
Revises: f2d80e99857a
Create Date: 2025-05-13 15:03:57.953847

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'efef24738f69'
down_revision: Union[str, None] = 'f2d80e99857a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_mobile', table_name='auth_identities')
    op.create_index('idx_mobile', 'auth_identities', ['mobile', 'deleted'], unique=True, postgresql_where=sa.text("identity_type = 'mobile'"))
    op.drop_index('idx_wechat_openid', table_name='auth_identities')
    op.create_index('idx_wechat_openid', 'auth_identities', ['wechat_openid', 'deleted'], unique=True, postgresql_where=sa.text("identity_type = 'wechat'"))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_wechat_openid', table_name='auth_identities', postgresql_where=sa.text("identity_type = 'wechat'"))
    op.create_index('idx_wechat_openid', 'auth_identities', ['wechat_openid'], unique=True)
    op.drop_index('idx_mobile', table_name='auth_identities', postgresql_where=sa.text("identity_type = 'mobile'"))
    op.create_index('idx_mobile', 'auth_identities', ['mobile'], unique=True)
    # ### end Alembic commands ###
