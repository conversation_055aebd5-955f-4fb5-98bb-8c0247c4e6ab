#-*-encoding: utf-8 -*-
from fastapi import <PERSON>AP<PERSON>
from app.api.router import api_router
# from config.settings import settings
# from app.core.logger import init_logging
# from app.core.before_request import init_http
from app.core.exception import init_exception
from app import utils, constants


def init_app():
    if utils.get_env() == constants.PRODUCTION:
        app = FastAPI(title=constants.SERVER_NAME,
                      description="metric-server",
                      version="V1.0.0")
    else:
        app = FastAPI(title=constants.SERVER_NAME,
                      description="metric-server",
                      version="V1.0.0")
    # Plugin => logger
    # init_logging()
    # Plugin => middleware
    # init_http(app)
    # Plugin => exception
    init_exception(app)
    # Plugin => router
    app.include_router(api_router)
    return app


app = init_app()
