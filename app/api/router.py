from fastapi import APIRouter

from app.api.api_v1 import api_auth, api_gua

api_router = APIRouter()

# v1
# api_router.include_router(api_login.router, prefix="/v1/login", tags=["登录"])
# api_router.include_router(api_user.router, prefix="/v1/user", tags=["用户"])
api_router.include_router(api_auth.router, prefix="/v1/auth", tags=["认证"])
api_router.include_router(api_gua.router, prefix="/v1/gua", tags=["问卦"])
