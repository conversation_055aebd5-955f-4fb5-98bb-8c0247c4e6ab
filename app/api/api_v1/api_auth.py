from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from app import service, schemas
from app.core import deps, response_model

router = APIRouter()


@router.post("/wechat/login", response_model=response_model.RenderBase[schemas.LoginResponse], summary="微信登录接口")
async def wechat_login(obj_in: schemas.WechatLoginRequest,
                       db: AsyncSession = Depends(deps.get_db)):
    return await service.auth_service.wechat_login(db, obj_in.code, obj_in.ip)


@router.post("/mobile/register", response_model=response_model.RenderBase[schemas.LoginResponse], summary="手机号注册接口")
async def mobile_register(user: schemas.UserCreate,
                          db: AsyncSession = Depends(deps.get_db)):
    return await service.auth_service.mobile_register(db, mobile=user.mobile,
                                                      password=user.password,
                                                      ip=user.ip)


@router.post("/token", response_model=response_model.RenderBase[schemas.TokenResponse], summary="密码模式登录（手机号/邮箱/用户名）")
async def login_for_token(form_data: OAuth2PasswordRequestForm = Depends(),
                          db: AsyncSession = Depends(deps.get_db)):

    identity_type = schemas.AuthType(form_data.username.split(":")[0]),
    identifier = form_data.username.split(":")[1],
    credential = form_data.password
    return await service.auth_service.authenticate_user(db, identity_type, identifier, credential)

@router.post("/refresh", response_model=response_model.RenderBase[schemas.TokenResponse], summary="刷新访问令牌")
async def refresh_token(refresh_token: str, db: AsyncSession = Depends(deps.get_db)):
    return await service.auth_service.refresh_token(db, refresh_token)
