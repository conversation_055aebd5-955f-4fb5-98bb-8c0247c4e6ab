from fastapi import APIRouter, Depends, Query
from typing import Optional
from sqlalchemy.orm import Session

from app import schemas, service
from app.core import response_model, deps

router = APIRouter()


@router.get("/select", summary="获取用户列表")
def get_select(db: Session = Depends(deps.get_db),
               current_user=Depends(deps.get_current_user)):
    return service.user.get_select(db)


@router.get("/",
            response_model=response_model.RenderPage[schemas.User],
            summary="获取用户分页列表")
def get_user_page(
        page: int = Query(1, title="显示页码"),
        pageSize: int = Query(10, title="显示条数"),
        username: Optional[str] = Query(None, title="用户名称"),
        email: Optional[str] = Query(None, title="用户邮箱"),
        current_user=Depends(deps.get_current_user),
        db: Session = Depends(deps.get_db),
):
    return service.user.get_page(db, page, pageSize, username, email)


@router.post("/", response_model=response_model.RenderBase, summary="新增用户")
def create_user(user_in: schemas.UserCreate,
                current_user=Depends(deps.get_current_user),
                db: Session = Depends(deps.get_db)):
    return service.user.create(db, user_in, current_user)


@router.put("/{id}", response_model=response_model.RenderBase, summary="编辑用户")
def update_user(id: int,
                user_in: schemas.UserUpdate,
                current_user=Depends(deps.get_current_user),
                db: Session = Depends(deps.get_db)):
    return service.user.update(db, id, user_in, current_user)
