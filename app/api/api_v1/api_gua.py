from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from app import service, schemas
from app.core import deps, response_model

router = APIRouter()


@router.post("/query", response_model=response_model.RenderBase[schemas.GuaResponse], summary="问卦接口")
async def query_gua(
    obj_in: schemas.GuaRequest,
    request: Request,
    db: AsyncSession = Depends(deps.get_db),
    is_frequency_limit = deps.rate_limiter(limit=1, period=3, key_prefix="interval"), # Apply rate limiting
    is_limit = deps.rate_limiter(limit=100, period=86400, key_prefix="daily")
      # Apply rate limiting
):
    """
    问卦接口，根据用户提问随机选择一个卦象并生成解释
    
    限制：每个用户每天最多调用3次
    """
    rate_info = request.state.rate_limit
    current = rate_info["current"]
    remaining = rate_info["remaining"]
    reset_time = rate_info["reset"]
    return await service.gua.query(obj_in.question, current, remaining, reset_time)
