from sqlalchemy import Column, String, Integer, Text, ForeignKey, Enum
from .base import Base


class AuthLog(Base):
    __tablename__ = "auth_logs"
    __name__ = "认证日志表"
    
    # 关联字段
    user_id = Column(ForeignKey("users.id"), index=True, comment="用户ID")
    auth_identity_id = Column(ForeignKey("auth_identities.id"), comment="认证方式ID")
    
    # 操作信息
    auth_type = Column(
        Enum('login', 'register', 'bind', 'unbind', name="auth_action"),
        nullable=False,
        comment="操作类型"
    )
    result = Column(Integer, nullable=False, comment="结果: 0=失败, 1=成功")
    
    # 设备信息
    ip_address = Column(String(45), comment="IP地址")
    device_info = Column(Text, comment="设备信息")
    user_agent = Column(String(255), comment="用户代理")
    
    # 错误信息
    error_code = Column(String(50), comment="错误代码")