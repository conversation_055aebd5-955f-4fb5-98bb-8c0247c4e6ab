from datetime import datetime
import pytz
from sqlalchemy import Column, Integer, String, DateTime, SmallInteger
from sqlalchemy.ext.declarative import declarative_base

DB_Base = declarative_base()


class Base(DB_Base):
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    created_at = Column(
        DateTime,
        default=lambda: datetime.now(pytz.timezone("Asia/Shanghai")),
        comment="创建时间")
    created_by = Column(String(100), comment="创建人")
    updated_at = Column(
        DateTime,
        default=lambda: datetime.now(pytz.timezone("Asia/Shanghai")),
        onupdate=lambda: datetime.now(pytz.timezone("Asia/Shanghai")),
        comment="更新时间")
    updated_by = Column(String(100), comment="更新人")
    deleted = Column(SmallInteger, default=0, comment="逻辑删除: 0=正常, 1=删除")

    __table_args__ = (
        {'info': {'order_by': ['id', 'created_at', 'created_by', 'updated_at', 'updated_by', 'deleted']}},
    )
    
    def to_dict(self):
        return {column.name: getattr(self, column.name) for column in self.__table__.columns}
    
    def __repr__(self):
        return f"<{self.__class__.__name__} {self.id}>"
