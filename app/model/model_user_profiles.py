from sqlalchemy import Column, String, Date, Enum, ForeignKey
from .base import Base

class UserProfile(Base):
    __tablename__ = "user_profiles"
    __name__ = "用户资料表"
    
    # 主键即外键
    user_id = Column(ForeignKey("users.id"), primary_key=True, comment="用户ID")
    
    # 基本信息
    real_name = Column(String(50), comment="真实姓名")
    gender = Column(Enum('male', 'female', 'unknown', name="gender"), 
                  default='unknown', comment="性别")
    birthday = Column(Date, comment="生日")
    
    # 联系信息
    email = Column(String(100), comment="电子邮箱")
    province = Column(String(50), comment="省份")
    city = Column(String(50), comment="城市")
    
    # 其他信息
    id_card = Column(String(18), comment="身份证号")