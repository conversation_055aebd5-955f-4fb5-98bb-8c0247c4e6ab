from datetime import datetime, timedelta
from sqlalchemy import Column, String, DateTime, ForeignKey
from .base import Base


class UserSession(Base):
    __tablename__ = "user_sessions"
    __name__ = "用户会话表"
    
    # 关联字段
    user_id = Column(ForeignKey("users.id"), nullable=False, index=True, comment="用户ID")
    auth_identity_id = Column(ForeignKey("auth_identities.id"), comment="认证方式ID")
    
    # 令牌信息
    access_token = Column(String(512), unique=True, index=True, nullable=False, comment="访问令牌")
    refresh_token = Column(String(512), unique=True, index=True, nullable=False, comment="刷新令牌")
    expires_at = Column(DateTime, nullable=False, comment="过期时间")
    
    # 设备信息
    device_id = Column(String(100), comment="设备ID")
    platform = Column(String(20), comment="平台: ios/android/web")
    ip_address = Column(String(45), comment="登录IP")