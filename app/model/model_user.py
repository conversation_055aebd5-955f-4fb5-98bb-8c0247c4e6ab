# -*-encoding: utf-8 -*-
from sqlalchemy import Column, String, Integer
from sqlalchemy.orm import relationship
from .base import Base


class User(Base):
    __tablename__ = "users"
    __name__ = "用户主表"

    # 状态字段
    status = Column(Integer, default=1, comment="状态: 0=禁用, 1=正常")

    # 通用信息
    nickname = Column(String(64), comment="昵称")
    avatar_url = Column(String(255), comment="头像URL")

    # 关系会在关联模型中定义

    # 关系（可选）
    # wechat_auth = relationship("UserWechatAuth", back_populates="user")
