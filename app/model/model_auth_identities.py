from sqlalchemy import Column, String, Integer, Enum, ForeignKey, DateTime, Index
from .base import Base


class AuthIdentity(Base):
    __tablename__ = "auth_identities"
    __name__ = "认证身份表"

    # 关联字段
    user_id = Column(ForeignKey("users.id"), nullable=False,
                     index=True, comment="用户ID")

    # 认证类型
    identity_type = Column(
        Enum('wechat', 'mobile', 'email', name="auth_type"),
        nullable=False,
        comment="认证类型"
    )

    # 微信专用字段
    wechat_openid = Column(String(64), index=True, comment="微信openid")
    wechat_unionid = Column(String(64), index=True, comment="微信unionid")
    session_key = Column(String(64), comment="微信会话密钥")

    # 手机号专用字段
    mobile = Column(String(20), index=True, comment="手机号")
    mobile_verified = Column(Integer, default=0, comment="手机验证: 0=未验证, 1=已验证")

    # 通用凭证字段
    credential = Column(String(255), comment="加密凭证(密码等)")

    # 索引配置
    __table_args__ = (
        # 微信openid唯一索引
        Index('idx_wechat_openid', 'wechat_openid', 'deleted', unique=True,
              postgresql_where=(identity_type == 'wechat')),

        # 手机号唯一索引
        Index('idx_mobile', 'mobile', 'deleted', unique=True,
              postgresql_where=(identity_type == 'mobile')),
    )
