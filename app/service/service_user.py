from app import crud, schemas
from app.core import custom_exception, response_model, security
from app import constants


class User:

    def get_select(self, db):
        data = crud.user_entity.get_user_list(db)
        return response_model.RenderList(data=data)

    def get_page(self, db, page, pageSize, username, email):
        # get query data
        total, items = crud.user.get_page_by_query(db, page, pageSize,
                                                   username, email)
        # assemble page info
        page = response_model.Page(page=page,
                                   pageSize=pageSize,
                                   total=total,
                                   pageData=items)
        # return reponse
        return response_model.RenderPage(data=page)

    def create(self, db, obj_in, current_user):
        # raise exception if username existed
        if crud.user.get_by_filter_one(db, {"username": obj_in.username}):
            raise custom_exception.ParamError(message="该名称的用户已存在")
        # raise exception if email existed
        if crud.user.get_by_filter_one(db, {"email": obj_in.email}):
            raise custom_exception.ParamError(message="该邮箱绑定的用户已存在")
        # init password
        obj_in.password = security.get_password_hash(obj_in.password)
        user = crud.user.create(db, obj_in, created_by=current_user.id)
        return response_model.RenderBase(message="新增成功")

    def update(self, db, id, obj_in, current_user):
        db_obj = crud.user.get(db, id)
        if not db_obj:
            raise custom_exception.NotFound(message="用户不存在")
        if db_obj.email != obj_in.email and crud.user.get_by_filter_one(
                db, {"email": obj_in.email}):
            raise custom_exception.ParamError(message="该邮箱绑定的用户已存在")
        if db_obj.username != obj_in.username and crud.user.get_by_filter_one(
                db, {"username": obj_in.username}):
            raise custom_exception.ParamError(message="该名称的用户已存在")
        obj_in.password = security.get_password_hash(obj_in.password)
        db_obj = crud.user.update(db,
                                  db_obj,
                                  obj_in,
                                  updated_by=current_user.id)
        return response_model.RenderBase(message="更新成功")


user = User()
