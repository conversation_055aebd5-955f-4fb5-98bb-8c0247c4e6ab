from jose import JWTError, jwt, ExpiredSignatureError
from passlib.context import CryptContext
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.future import select
# from .models import User, AuthIdentity, UserSession, AuthLog
from app import schemas, model, crud
from sqlalchemy.ext.asyncio import AsyncSession
from loguru import logger
from app.core import custom_exception, response_model
from conf.settings import settings
import secrets
import httpx
from fastapi import HTTPException


class AuthService:

    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
        self.SECRET_KEY = settings.SECRET_KEY
        self.ALGORITHM = settings.ALGORITHM
        self.ACCESS_TOKEN_EXPIRE = timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        self.wx_appid = settings.WX_APPID
        self.wx_secret = settings.WX_SECRET
        self.url = settings.JSCODE2SESSION_URL

    async def _verify_wechat(self, session_key: str, credential: str) -> bool:
        """
        验证微信登录凭证
        :param session_key: 微信会话密钥
        :param credential: 客户端提交的加密数据（如encryptedData/iv）
        :return: 是否验证通过
        """
        try:
            # 示例1：简单验证session_key是否有效
            if not session_key or len(session_key) != 24:
                return False

            # 示例2：解密用户信息（需安装pycryptodome）
            # from wechat_decrypt import WXBizDataCrypt
            # crypt = WXBizDataCrypt(app_id, session_key)
            # decrypted = crypt.decrypt(credential['encryptedData'], credential['iv'])
            # return decrypted['watermark']['appid'] == app_id

            # 示例3：验证签名
            # import hashlib
            # raw_str = f"{session_key}{credential['rawData']}"
            # signature = hashlib.sha1(raw_str.encode()).hexdigest()
            # return signature == credential['signature']

            return True  # 实际项目需替换为真实验证逻辑

        except Exception as e:
            logger.error(f"微信验证失败: {str(e)}")
            return False

    async def authenticate_user(self, db: AsyncSession, identity_type: schemas.AuthType, identifier: str,
                                credential: str) -> Optional[model.User]:
        """通用认证方法"""
        auth_identity = await crud.auth.get_auth_identity(db, identity_type, identifier)
        if not auth_identity:
            return None
        # 验证凭证
        if identity_type == schemas.AuthType.WECHAT:
            valid = await self._verify_wechat(auth_identity.session_key,
                                              credential)
        else:
            valid = self.pwd_context.verify(credential,
                                            auth_identity.credential)
        if not valid:
            return None

        # 返回关联用户
        user = await crud.auth.get_user_by_user_id(db, auth_identity)

        if not user:
            raise custom_exception.AuthError(message="认证失败")

        session = await crud.auth.create_user_session(
            db,
            user_id=user.id,
            auth_identity_id=user.primary_identity.id,
            ip=None,
            platform="api",
            access_token=self._create_access_token(user.id),
            refresh_token=self._create_refresh_token(user.id)
        )

        res = {"access_token": session.access_token,
               "token_type": "bearer", "expires_in": 1800}
        logger.debug(res)
        return response_model.RenderBase(data=res, message="success") 

    async def wechat_login(self, db: AsyncSession, code: str, ip: str = None) -> dict:
        """微信登录流程"""
        # 1. 获取微信openid
        wechat_data = await self._get_wechat_session(code)

        # 2. 查找或创建用户
        user, auth_identity = await crud.auth.find_or_create_wechat_user(db, wechat_data)

        # 3. 创建会话
        session = await crud.auth.create_user_session(
            db,
            user_id=user.id,
            auth_identity_id=auth_identity.id,
            ip=ip,
            platform="wechat",
            access_token=self._create_access_token(user.id),
            refresh_token=self._create_refresh_token(user.id)
        )

        # 4. 记录日志
        await crud.auth.log_auth_action(db, user_id=user.id,
                                        auth_type="login",
                                        identity_id=user.id,
                                        ip=ip,
                                        result=True)

        res = {
            "access_token": session.access_token,
            "refresh_token": session.refresh_token,
            "token_type": "bearer",
            "expires_in": int(
                (datetime.utcnow() + timedelta(minutes=30) -
                 datetime.utcnow()).total_seconds()
            ),
            "user": {
                "id": user.id,
                "nickname": user.nickname,
                "avatar_url": user.avatar_url
            }
        }
        logger.debug(res)
        return response_model.RenderBase(data=res, message="登录成功")

    async def mobile_register(self,
                              db,
                              mobile: str,
                              password: str,
                              ip: str = None) -> dict:
        """手机号注册"""
        # 验证手机号唯一性
        if await crud.auth.mobile_exists(db, mobile):
            raise custom_exception.ParamError("手机号已注册")

        # 创建用户
        user = await crud.auth.create_user_by_mobile(db, mobile)

        # 添加手机认证身份
        auth_identity = await crud.auth.create_auth_identity_by_mobile(
            db,
            user_id=user.id,
            identity_type=schemas.AuthType.MOBILE,
            mobile=mobile,
            mobile_verified=False,    # 需要短信验证
            credential=self.pwd_context.hash(password))

        # 创建会话（可选）
        session = await crud.auth.create_user_session(
            db,
            user_id=user.id,
            auth_identity_id=auth_identity.id,
            ip=ip,
            platform="mobile",
            access_token=self._create_access_token(user.id),
            refresh_token=self._create_refresh_token(user.id)
        )
        res = {
            "access_token": session.access_token,
            "refresh_token": session.refresh_token,
            "token_type": "bearer",
            "expires_in": 1800,  # 30分钟
            "user": {
                "id": user.id,
                "nickname": user.nickname,
                "avatar_url": user.avatar_url,
                "mobile": mobile

            }
        }
        # res = {"access_token": session.access_token, "user": user.to_dict()}
        return response_model.RenderBase(data=res, message="新增成功")

    def _create_access_token(self, user_id: int) -> str:
        """生成JWT令牌"""
        return jwt.encode(
            {
                "sub": str(user_id),
                "exp": datetime.utcnow() + self.ACCESS_TOKEN_EXPIRE
            },
            self.SECRET_KEY,
            algorithm=self.ALGORITHM)
    
    
    def _create_refresh_token(self, user_id: int) -> str:
        """
        生成刷新令牌
        :param user_id: 用户ID
        :return: JWT格式的刷新令牌
        """
        # 生成唯一标识符增强安全性
        jti = secrets.token_hex(16)

        payload = {
            "sub": str(user_id),
            "exp": datetime.utcnow() + timedelta(days=7),  # 7天有效期
            "type": "refresh",  # 明确令牌类型
            "jti": jti,        # 唯一标识符
        }

        return jwt.encode(
            payload,
            self.SECRET_KEY,    # 使用与access_token相同的密钥
            algorithm=self.ALGORITHM
        )

    async def _get_wechat_session(self, code: str) -> dict:
        """
        调用微信code2session接口
        文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
        """

        params = {
            "appid": self.wx_appid,
            "secret": self.wx_secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.url, params=params)
                data = response.json()

            if "errcode" in data:
                raise HTTPException(
                    status_code=400,
                    detail=f"微信接口错误: {data.get('errmsg', '未知错误')} (代码: {data['errcode']})"
                )

            return {
                "openid": data["openid"],
                "session_key": data["session_key"],
                "unionid": data.get("unionid")  # 可选字段
            }

        except httpx.RequestError as e:
            raise HTTPException(
                status_code=503,
                detail=f"微信服务不可用: {str(e)}"
            )

    async def refresh_token(self, db: AsyncSession, refresh_token: str) -> dict:
        """
        刷新访问令牌
        :param db: 数据库会话
        :param refresh_token: 刷新令牌
        :return: 新的访问令牌和刷新令牌
        """
        try:
            # 解析刷新令牌
            payload = jwt.decode(
                refresh_token,
                self.SECRET_KEY,
                algorithms=[self.ALGORITHM]
            )
            
            # 验证令牌类型
            if payload.get("type") != "refresh":
                raise custom_exception.AuthError(message="无效的刷新令牌类型")
            
            user_id = int(payload.get("sub"))
            
            # 查询会话记录
            from sqlalchemy.future import select
            from app.model.model_user_sessions import UserSession
            
            stmt = select(UserSession).where(
                (UserSession.user_id == user_id) &
                (UserSession.refresh_token == refresh_token) &
                (UserSession.deleted == 0)
            )
            session = (await db.execute(stmt)).scalar_one_or_none()
            
            if not session:
                raise custom_exception.AuthError(message="刷新令牌已失效")
                
            # 生成新的令牌
            new_access_token = self._create_access_token(user_id)
            new_refresh_token = self._create_refresh_token(user_id)
            
            # 更新会话记录
            session.access_token = new_access_token
            session.refresh_token = new_refresh_token
            session.expires_at = datetime.utcnow() + self.ACCESS_TOKEN_EXPIRE
            await db.commit()
            
            res = {
                "access_token": new_access_token,
                "refresh_token": new_refresh_token,
                "token_type": "bearer",
                "expires_in": int(self.ACCESS_TOKEN_EXPIRE.total_seconds())
            }
            
            return response_model.RenderBase(data=res, message="令牌刷新成功")
            
        except ExpiredSignatureError:
            raise custom_exception.AuthError(message="刷新令牌已过期")
        except JWTError:
            raise custom_exception.AuthError(message="无效的刷新令牌")


auth_service = AuthService()
