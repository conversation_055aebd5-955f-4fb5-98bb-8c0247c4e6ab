#-*-encoding: utf-8 -*-

import time
from datetime import datetime
from loguru import logger
from fastapi import FastAPI, Request
from app.core.security import parse_token


def init_http(app: FastAPI):
    # Combined middleware for request processing and response headers
    @app.middleware("http")
    async def request_middleware(request: Request, call_next):
        start_time = time.time()

        # Parse authorization token
        authorization = request.headers.get('authorization')
        # If login, get user id
        if authorization:
            request.state.payload = parse_token(
                authorization.replace("Bearer ", ""))

        # Process the request
        response = await call_next(request)

        # Add process time header
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time * 1000)

        # Add rate limit headers if available
        if hasattr(request.state, "rate_limit"):
            rate_info = request.state.rate_limit
            response.headers["X-RateLimit-Limit"] = str(rate_info["limit"])
            response.headers["X-RateLimit-Remaining"] = str(rate_info["remaining"])
            response.headers["X-RateLimit-Reset"] = rate_info["reset"].strftime("%Y-%m-%dT%H:%M:%SZ")

        return response
