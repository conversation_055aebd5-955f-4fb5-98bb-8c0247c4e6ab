class ParamError(Exception):

    def __init__(self, message: str = "Request Param Error"):
        self.message = message


class TokenError(Exception):

    def __init__(self, message: str = "Token Error"):
        self.message = message


class NotFound(Exception):

    def __init__(self, message: str = "Not Found"):
        self.message = message


class AuthError(Exception):

    def __init__(self, message: str = "Authentication Error"):
        self.message = message

class ServiceError(Exception):

    def __init__(self, message: str = "Service Error"):
        self.message = message
