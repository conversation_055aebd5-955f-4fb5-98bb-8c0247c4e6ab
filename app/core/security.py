#-*-encoding: utf-8 -*-

import secrets
from jose import jwt
from passlib.context import CryptContext
from datetime import datetime, timedelta
from pydantic import ValidationError
from app import constants
from jose import jwt, J<PERSON><PERSON>rror, ExpiredSignatureError
from conf.settings import settings

pwd_context = CryptContext(schemes=["argon2"], deprecated="auto")

ALGORITHM = "HS256"


def generate_token(subject, expires_delta=400) -> str:
    # valid time
    expire = datetime.utcnow() + timedelta(minutes=expires_delta)
    # encode
    to_encode = {"exp": expire, "sub": str(subject)}
    # jwt
    encoded_jwt = jwt.encode(to_encode,
                             constants.SECRET_KEY,
                             algorithm=ALGORITHM)
    # return
    return encoded_jwt


def parse_token(token):
    try:
        return jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
    except (jwt.<PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationError):
        # raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Could not validate credentials")
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def decode_token(token: str) -> dict:
    """解码JWT令牌"""
    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        return payload
    except ExpiredSignatureError:
        # Token 已过期
        return None
    except JWTError:
        # Token 无效
        return None

