# -*-encoding: utf-8 -*-

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.engine.url import URL
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from conf.settings import settings

engine = create_engine(settings.SQLALCHEMY_DATABASE_URI,
                       pool_pre_ping=True,
                       pool_size=30,
                       max_overflow=100)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


async_engine = create_async_engine(
    settings.ASYNC_SQLALCHEMY_DATABASE_URI,
    pool_pre_ping=True,
    pool_size=30,
    max_overflow=100,
    # echo=True  # 开发时显示SQL日志
)

AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False
)
