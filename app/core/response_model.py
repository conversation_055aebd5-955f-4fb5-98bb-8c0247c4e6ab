# -*-encoding: utf-8 -*-

from pydantic import BaseModel
from typing import Generic, TypeVar, List

T = TypeVar("T")


class Page(BaseModel, Generic[T]):
    page: int = 1    # type: ignore
    pageSize: int = 10    # type: ignore
    total: int = 0    # type: ignore
    pageData: List[T] = []


class RenderBase(BaseModel, Generic[T]):
    code: int = 10200    # first version api code,
    data: T = ""
    message: str = ""


class RenderPage(BaseModel, Generic[T]):
    code: int = 10200    # first version api code,
    data: Page[T] = ""
    message: str = ""


class RenderList(BaseModel, Generic[T]):
    code: int = 10200    # first version api code,
    data: List[T] = []
    message: str = ""
