PRODUCTION = "production"
DEVELOPMENT = "development"
DEFAULT_TIMEZONE = "Asia/Shanghai"

HEADERS = {"Content-Type": "application/json"}
# dump config option
OPTION_HOST = "host"
OPTION_USER = "user"
OPTION_PORT = "port"
OPTION_PASSWORD = "password"
OPTION_DB = "db"

SECTION_DATA_PIPELINE_DEV = "data_pipeline_dev"
SECTION_DATA_PIPELINE_PROD = "data_pipeline_prod"

SERVER_NAME = "metric-server"
SECRET_KEY = "DtyE8XKW4-6s8JPFbfGHXTMtLAXXQ3JUYvL249vXdBk"
DEFAULT_PASSWORD = "123456"

KEYCLOAK_DEV = "keycloak_dev"
KEYCLOAK_PROD = "keycloak_prod"

KEYCLOAK_SERVER_URL = "server_url"
KEYCLOAK_CLIENT_ID = "client_id"
KEYCLOAK_REALM_NAME = "realm_name"
KEYCLOAK_CLIENT_SECRET_KEY = "secret_key"

LOG_DEV = "log_dev"
LOG_PROD = "log_prod"

LOG_ENDPOINT = "log_endpoint"
LOG_ACCESS_KEY_ID = "log_access_key_id"
LOG_ACCESS_KEY = "log_access_key"
LOG_PROJECT = "log_project"
LOG_LOGSTORE = "log_logstore"
LOG_TOPIC = "log_topic"

EMAIL = "email"

EMAIL_SERVER = "server"
EMAIL_USERNAME = "username"
EMAIL_PASSWORD = "password"
EMAIL_SENDER = "sender"

ALERT_EMAIL_SUBJECT = "{task_name}-告警"
ALERT_EMAIL_BODY = """
{task_name}-告警
指标名称：{metric_name}
开始时间：{started_at}
结束时间：{finished_at}
异常数量：{count}条
告警规则：{rule_name}
监测时间：{running_at}
详情地址：{link_url}
"""

NOTIFY_SUBSCRIBER_EMAIL_UPDATE_SUBJECT = "{task_name}-变更"
NOTIFY_SUBSCRIBER_EMAIL_UPDATE_BODY = ""

TEST_SUBJECT = "测试"
TEST_BODY = "这是一条测试消息"

SECTION_SUPERSET = "superset"

TITANIC_SERVER_URL = "titanic_server_url"
TITANIC_DEV = "titanic_dev"
TITANIC_PROD = "titanic_prod"

FALCON_URL = "falcon_url"
FALCON_DEV = "falcon_dev"
FALCON_PROD = "falcon_prod"

SECTION_STARROCKS = "starrocks"

NOTIFY_TEMPLATES = {
    'new_subscriber': {
        'subject': "测试",
        'template': "这是一条测试消息"
    },
    'edit_subscriber_linked': {
        'subject':
        "指标异动 - 订阅已被移除",
        'template':
        "当前订阅已被移除，包含异动任务：{{ task }}， 操作人：{{ operator }}，操作时间：{{ operate_time }}"
    },
    'delete_subscriber_linked': {
        'subject': "删除失败通知",
        'template': "不能删除，提示已关联多个告警任务, 请下线任务后操作 *确认按钮"
    },
    'delete_subscriber_unlinked': {
        'subject': "删除成功通知",
        'template': "无关联，直接删除，弹窗提示: 删除成功"
    },
    'new_rule': {
        'subject':
        "新建异动任务通知",
        'template':
        "异动任务名称：{{ task }}，启用时间为{{ start_date }}，下线时间为{{ end_date }} 创建人：{{ operator }}"
    },
    'edit_rule': {
        'subject':
        "编辑异动任务通知",
        'template':
        "异动任务名称：{{ task }} ，任务和规则已变更，变更时间为{{ operate_time }}，启用时间为{{ start_date }}，下线时间为{{ end_date }}，操作人：{{ operator }}"
    },
    'disable_rule': {
        'subject':
        "下线异动任务通知",
        'template':
        "异动任务名称：{{ task }} ，任务和规则已下线，下线时间为{{ operate_time }}，操作人：{{ operator }}"
    },
    'delete_rule': {
        'subject':
        "删除异动任务通知",
        'template':
        "异动任务名称：{{ task }} ，任务和规则已被删除，删除时间为{{ operate_time }}，操作人：{{ operator }}"
    },
    'execution_notify': {
        'subject':
        "告警通知 - {{ task }} - {{ execution_date }}",
        'template':
        """
        开始时间：{{start_date}}
        结束时间：{{end_date}}
        异常数量：{{count}}条
        监测时间：{{running_at}}
        详情地址：{{link_url}}
        """
    }
}

NOTIFY_CONTEXT = {
    "task": None,
    "operator": None,
    "operate_time": None,
    # "change_info": None,
    "execution_date": None,
    "start_date": None,
    "end_date": None,
    "count": None,
    "running_at": None,
    "link_url": None
}

METRICS_UPLOAD_MAPPING = {
    "指标大类": "metric_class",
    "指标名称": "metric_name",
    "指标版本": "version",
    "指标状态": "metric_status",    # 需要映射
    "所属分类": "metric_category_id",    # metrics_category_info metric_category_id
    "指标层级": "metric_level",    # 导入数字
    "所属功能": "metric_func_id",    # metrics_func_info metric_func_id
    "指标标识": "metric_key",
    "指标定义": "metric_def",
    "计算公式": "calc_formula",
    "所属功能属性": "metric_func_prop_id",    # metrics_func_prop metric_func_prop_id
    "研发/业务指标": "metric_props",
    "计算公式": "calc_formula",
    "上线时间": "release_time",
    "是否人工统计": "is_manual",    # 否 0 是 1
    "备注": "comment",
    "指标单位": "unit",
    "kpi_owner": "kpi_owner_id",    # 人名 id
    "指标定义人": "def_owner_id",    # 人名 id
    "计算逻辑定义人": "calc_owner_id",    # 人名 id
    "数据开发人": "dw_owner_id",    # 人名 id
    "建议和评论": "sugg_comments",
    "指标类型": "type",    # 原子指标 1 复合指标 2
    "表达式": "expression",
    "表达式-简洁版": "expression_pure",
    "数据表名称": "table_name",
    "详细计算逻辑": "calc_detail",
    "column list": "core_info_list",
    "metric_name list": "metrics_list",
    "诊断id（diagnostic_id/diagnostic_type/diagnostic_type）list":
    "diagnostic_list"
}
METRICS_UPLOAD_VALUE_MAPPING = {
    "metric_status": {
        "准备中": 1,
        "已上线": 2,
        "已下线": 3
    },
    "is_manual": {
        "否": 0,
        "是": 1,
        "no": 0
    },
    "type": {
        "原子指标": 1,
        "复合指标": 2
    }
}
