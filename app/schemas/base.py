# -*- encoding: utf-8 -*-

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, validator, Field


# Shared properties to return via API
class BaseSchemas(BaseModel):
    id: int = Field(..., description="id")
    created_at: Optional[datetime] = Field(None, description="创建时间")
    created_by: Optional[int] = Field(None, description="创建人")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    updated_by: Optional[int] = Field(None, description="更新人")

    @validator("created_at")
    def format_created_at(cls, v):
        return v.strftime("%Y-%m-%d %H:%M:%S") if v else None

    @validator("updated_at")
    def format_updated_at(cls, v):
        return v.strftime("%Y-%m-%d %H:%M:%S") if v else None

    class Config:
        orm_mode = True
