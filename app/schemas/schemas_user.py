#-*-encoding: utf-8 -*-
from typing import Optional

from pydantic import BaseModel, Field

from .base import BaseSchemas


# Properties to receive via API on creation
class UserCreate(BaseModel):
    hash_id: Optional[str] = Field(None, description='hash_id')
    email: Optional[str] = Field(None, description='email')
    username: Optional[str] = Field(None, description='first_name')
    first_name: Optional[str] = Field(None, description='first_name')
    last_name: Optional[str] = Field(None, description='last_name')
    enabled: Optional[str] = Field(None, description='enabled')
    password: Optional[str] = Field(None, description='hashed_password')


# Properties to receive via API on update
class UserUpdate(UserCreate):
    pass


class User(BaseSchemas):
    hash_id: Optional[str] = Field(None, description='hash_id')
    username: Optional[str] = Field(None, description="用户名称")
    email: Optional[str] = Field(None, description="用户邮箱")
    first_name: Optional[str] = Field(None, description='first_name')
    last_name: Optional[str] = Field(None, description='last_name')
