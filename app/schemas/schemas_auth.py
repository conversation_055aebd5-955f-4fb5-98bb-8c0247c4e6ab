from pydantic import BaseModel, EmailStr, Field
from enum import Enum
from typing import Optional, Literal

class AuthType(str, Enum):
    WECHAT = "wechat"
    MOBILE = "mobile"
    EMAIL = "email"
    USERNAME = "username"


class WechatLoginRequest(BaseModel):
    code: str
    ip: str = None


class UserCreate(BaseModel):
    mobile: str
    password: str
    ip: str = None


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str = None
    token_type: str = "bearer"
    expires_in: int = Field(..., description="令牌剩余有效期(秒)")


class UserResponse(BaseModel):
    id: int
    nickname: str
    avatar_url: str = None

class TokenBase(BaseModel):
    access_token: str
    token_type: Literal["bearer"] = "bearer"
    expires_in: int = Field(..., description="令牌剩余有效期(秒)")

class UserInfo(BaseModel):
    id: int
    nickname: str
    avatar_url: Optional[str] = None

class LoginResponse(TokenBase):
    refresh_token: str
    user: UserInfo

class RefreshResponse(TokenBase):
    pass

# 解决前向引用问题（新版本Pydantic写法）
UserInfo.model_rebuild()
LoginResponse.model_rebuild()
RefreshResponse.model_rebuild()