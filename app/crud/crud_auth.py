# -*- encoding: utf-8 -*-

from sqlalchemy.future import select

from app.model import AuthIdentity, AuthIdentity, User, UserSession, AuthLog
from app.crud.base import CRUDBase
from conf.settings import settings
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession


class AuthCRUD:

    async def get_auth_identity(self, db, identity_type, identifier):
        """
            get page data of user, and can filter by username, email
        """
        stmt = select(AuthIdentity).where(
            (AuthIdentity.identity_type == identity_type)
            & ((AuthIdentity.wechat_openid == identifier)
               | (AuthIdentity.mobile == identifier)
               | (AuthIdentity.identifier == identifier)))
        auth_identity = (await db.execute(stmt)).scalar_one_or_none()
        return auth_identity

    async def get_user_by_user_id(db, auth_identity):
        return await db.get(User, auth_identity.user_id) 

    async def get_user_by_id(db, user_id):
        return await db.get(User, user_id) 
    
    async def create_user_session(self, db, user_id: int, auth_identity_id: int,
                                  ip: str, platform: str, access_token, refresh_token) -> UserSession:
        """创建用户会话"""
        session = UserSession(
            user_id=user_id,
            auth_identity_id=auth_identity_id,
            access_token=access_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES),
            ip_address=ip,
            platform=platform)
        db.add(session)
        await db.commit()
        return session

    async def find_or_create_wechat_user(self, db, wechat_data: dict) -> User:
        """查找或创建微信用户"""
        # 查找现有身份
        stmt = select(AuthIdentity).where(
            (AuthIdentity.identity_type == "wechat") &
            (AuthIdentity.wechat_openid == wechat_data["openid"]) &
            (AuthIdentity.deleted == 0)
        )
        existing_identity = (await db.execute(stmt)).scalar_one_or_none()

        if existing_identity:
            user = (await db.execute(select(User).where(User.id == existing_identity.id))).scalars().first()
            # 更新会话密钥
            existing_identity.session_key = wechat_data["session_key"]
            await db.commit()
            return user, existing_identity

        # 创建新用户
        user = User(
            nickname=f"微信用户{wechat_data['openid'][-4:]}",
            created_by="wechat",
            updated_by="wechat"
        )
        db.add(user)
        await db.commit()
        await db.refresh(user)

        # 创建认证身份
        auth_identity = AuthIdentity(
            user_id=user.id,
            identity_type="wechat",
            wechat_openid=wechat_data["openid"],
            session_key=wechat_data["session_key"],
            wechat_unionid=wechat_data.get("unionid")
        )
        db.add(auth_identity)
        await db.commit()

        return user, auth_identity

    async def log_auth_action(
        self,
        db,
        user_id: int,
        auth_type: str,
        identity_id: int,
        ip: str = None,
        result: bool = True
    ):
        """记录认证日志"""
        log = AuthLog(
            user_id=user_id,
            auth_identity_id=identity_id,
            auth_type=auth_type,
            result=1 if result else 0,
            ip_address=ip,
            created_by=str(user_id)
        )
        db.add(log)
        await db.commit()

    async def mobile_exists(self, db: AsyncSession, mobile: str) -> bool:
        """
        检查手机号是否已注册
        :param mobile: 待检查的手机号
        :return: 存在返回True，否则False
        """
        # 方法1：直接查询AuthIdentity表
        stmt = select(AuthIdentity).where(
            (AuthIdentity.identity_type == "mobile") &
            (AuthIdentity.mobile == mobile) &
            (AuthIdentity.deleted == 0)  # 考虑软删除
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none() is not None

    async def create_user_by_mobile(self, db, mobile):
        # 创建用户
        user = User(nickname=f"用户{mobile[-4:]}")
        db.add(user)
        await db.commit()
        return user

    async def create_auth_identity_by_mobile(self, db, user_id, identity_type, mobile, mobile_verified, credential):
        auth_identity = AuthIdentity(
            user_id=user_id,
            identity_type=identity_type,
            mobile=mobile,
            mobile_verified=mobile_verified,    # 需要短信验证
            credential=credential)
        db.add(auth_identity)
        await db.commit()
        return auth_identity


auth = AuthCRUD()
