from datetime import datetime
from pydantic import BaseModel
from typing import Generic, TypeVar
from fastapi.encoders import jsonable_encoder
import pytz

from app.model.base import Base

ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):

    def __init__(self, model):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).

        **Parameters**

        * `model`: A SQLAlchemy model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model: ModelType = model

    def get(self, db, id):
        return db.query(self.model).filter(self.model.id == id,
                                           self.model.deleted == 0).first()

    def get_by_filter(self, db, filter_dict={}):
        dataset = db.query(self.model).filter(self.model.deleted == 0)
        if filter_dict:
            dataset = dataset.filter_by(**filter_dict)
        return dataset.all()

    def get_by_filter_one(self, db, filter_dict={}):
        dataset = db.query(self.model).filter(self.model.deleted == 0)
        if filter_dict:
            dataset = dataset.filter_by(**filter_dict)
        return dataset.first()

    def create(self, db, obj_in, **kwargs):
        obj_in_data = jsonable_encoder(obj_in) if not isinstance(
            obj_in, dict) else obj_in
        db_obj = self.model(**obj_in_data,
                            **kwargs,
                            created_at=datetime.now(
                                tz=pytz.timezone("Asia/Shanghai")),
                            updated_at=datetime.now(
                                tz=pytz.timezone("Asia/Shanghai")),
                            deleted=0)    # type: ignore
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(self, db, db_obj, obj_in, updated_by=None):
        obj_data = jsonable_encoder(db_obj) if not isinstance(obj_in,
                                                              dict) else obj_in
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db_obj.updated_at = datetime.now(tz=pytz.timezone("Asia/Shanghai"))
        db_obj.updated_by = updated_by
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db, id):
        obj = db.query(self.model).get(id)
        db.delete(obj)
        db.commit()
        return obj

    def delete(self, db, db_obj, updated_by=None):
        db_obj.deleted = 1
        db_obj.updated_by = updated_by
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def batch_create(self, db, obj_list_in):
        db.execute(self.model.__table__.insert(), obj_list_in)
        db.commit()

    def batch_update(self, db, obj_in_list, updated_by=None):
        # Assuming obj_list_in contains dictionaries with 'id' and other fields to update
        for obj_in in obj_in_list:
            update_data = jsonable_encoder(obj_in) if not isinstance(
                obj_in, dict) else obj_in
            db_obj = db.query(self.model).filter(
                self.model.id == update_data.get("id"),
                self.model.deleted == 0).first()
            for field in update_data:
                if field in update_data:
                    setattr(db_obj, field, update_data[field])
            db_obj.updated_at = datetime.now(tz=pytz.timezone("Asia/Shanghai"))
            db_obj.updated_by = updated_by
            db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def batch_delete(self, db, filter_dict, updated_by=None):
        db.query(self.model).filter_by(**filter_dict).update({
            "deleted":
            1,
            "updated_by":
            updated_by,
            "updated_at":
            datetime.now(tz=pytz.timezone("Asia/Shanghai"))
        })
        db.commit()

    def batch_update_by_dict(self,
                             db,
                             filter_dict,
                             update_dict,
                             updated_by=None):
        update_dict = jsonable_encoder(update_dict) if not isinstance(
            update_dict, dict) else update_dict
        update_dict["updated_by"] = updated_by
        update_dict["updated_at"] = datetime.now(
            tz=pytz.timezone("Asia/Shanghai"))
        db.query(self.model).filter_by(**filter_dict).update(update_dict)
        db.commit()

    def bulk_save(self, db, obj_list_in, **kwargs):
        db_obj_list = [
            self.model(**obj,
                       **kwargs,
                       created_at=datetime.now(
                           tz=pytz.timezone("Asia/Shanghai")),
                       deleted=0) for obj in obj_list_in
        ]
        db.bulk_save_objects(db_obj_list)
        db.commit()

    def get_by_filter_and_page(self, db, page, page_size, filter_dict=None):
        dataset = db.query(self.model).filter(self.model.deleted == 0)
        if filter_dict:
            dataset = dataset.filter_by(**filter_dict)
        objs = dataset.limit(page_size).offset((page - 1) * page_size).all()
        count = dataset.count()
        return objs, count

    def count(self, db, filter_dict=None):
        dataset = db.query(self.model).filter(self.model.deleted == 0)
        if filter_dict:
            dataset = dataset.filter_by(**filter_dict)
        return dataset.count()

    def get_by_filter_like_and_page(self,
                                    db,
                                    page,
                                    page_size,
                                    filter_like_dict=None):
        """

        filter_like_dict: {"name", "123", "country": "chi"}

        """
        dataset = db.query(self.model).filter(self.model.deleted == 0)
        if filter_like_dict:
            for field, value in filter_like_dict.items():
                m_field = getattr(self.model, field, None)
                if not m_field:
                    continue
                dataset = dataset.filter(m_field.like(value))
        objs = dataset.limit(page_size).offset((page - 1) * page_size).all()
        count = dataset.count()
        return objs, count
