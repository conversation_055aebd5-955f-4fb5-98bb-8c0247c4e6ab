# -*- encoding: utf-8 -*-

from sqlalchemy.orm import Session

from app.model import User
from app.crud.base import CRUDBase
from app.schemas import UserCreate, UserUpdate


class UserCRUD(CRUDBase[User, UserCreate, UserUpdate]):

    def get_page_by_query(self, db, page, pageSize, username, email):
        """
            get page data of user, and can filter by username, email
        """

        # base query
        queryset = db.query(self.model).filter_by(deleted=0)

        # filter
        if username:
            queryset = queryset.filter_by(username=username)
        if email:
            queryset = queryset.filter_by(vehicle_num=email)
        # get all total
        total = queryset.count()
        # page data
        items = queryset.offset((page - 1) * pageSize).limit(pageSize).all()
        # return reponse
        return total, items


user = UserCRUD(User)
