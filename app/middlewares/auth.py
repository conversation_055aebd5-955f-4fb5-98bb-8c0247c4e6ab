from fastapi import Request, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt, JWTError
from conf.settings import settings


class JWTBearer(HTTPBearer):

    async def __call__(self, request: Request):
        credentials: HTTPAuthorizationCredentials = await super().__call__(
            request)
        if not credentials or credentials.scheme != "Bearer":
            raise HTTPException(status_code=403, detail="无效的认证信息")

        try:
            payload = jwt.decode(credentials.credentials,
                                 settings.SECRET_KEY,
                                 algorithms=[settings.ALGORITHM])
            request.state.user_id = int(payload["sub"])
        except JWTError:
            raise HTTPException(status_code=403, detail="令牌无效或已过期")

        return credentials.credentials
